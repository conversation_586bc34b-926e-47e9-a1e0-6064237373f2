package br.com.pedroabib.osmanager.os_manager.controllers;

import br.com.pedroabib.osmanager.os_manager.dto.ServiceOrderCreateDTO;
import br.com.pedroabib.osmanager.os_manager.dto.ServiceOrderResponseDTO;
import br.com.pedroabib.osmanager.os_manager.dto.ServiceOrderStatusUpdateDTO;
import br.com.pedroabib.osmanager.os_manager.services.ServiceOrderService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/service-orders")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class ServiceOrderController {

    private final ServiceOrderService serviceOrderService;

    /**
     * Criar uma nova Ordem de Serviço
     */
    @PostMapping
    public ResponseEntity<ServiceOrderResponseDTO> createServiceOrder(@RequestBody ServiceOrderCreateDTO createDTO) {
        try {
            ServiceOrderResponseDTO createdServiceOrder = serviceOrderService.createServiceOrder(createDTO);
            return ResponseEntity.status(HttpStatus.CREATED).body(createdServiceOrder);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Listar todas as Ordens de Serviço
     */
    @GetMapping
    public ResponseEntity<List<ServiceOrderResponseDTO>> getAllServiceOrders() {
        List<ServiceOrderResponseDTO> serviceOrders = serviceOrderService.getAllServiceOrders();
        return ResponseEntity.ok(serviceOrders);
    }

    /**
     * Buscar Ordem de Serviço por ID
     */
    @GetMapping("/{id}")
    public ResponseEntity<ServiceOrderResponseDTO> getServiceOrderById(@PathVariable Long id) {
        try {
            ServiceOrderResponseDTO serviceOrder = serviceOrderService.getServiceOrderById(id);
            return ResponseEntity.ok(serviceOrder);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Atualizar status da Ordem de Serviço
     */
    @PatchMapping("/{id}/status")
    public ResponseEntity<ServiceOrderResponseDTO> updateServiceOrderStatus(
            @PathVariable Long id,
            @RequestBody ServiceOrderStatusUpdateDTO statusUpdateDTO) {
        try {
            ServiceOrderResponseDTO updatedServiceOrder = serviceOrderService.updateServiceOrderStatus(id, statusUpdateDTO);
            return ResponseEntity.ok(updatedServiceOrder);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Endpoint adicional: Buscar por número da OS
     */
    @GetMapping("/order-number/{orderNumber}")
    public ResponseEntity<ServiceOrderResponseDTO> getServiceOrderByOrderNumber(@PathVariable String orderNumber) {
        // Este método pode ser implementado no service se necessário
        return ResponseEntity.status(HttpStatus.NOT_IMPLEMENTED).build();
    }
}
