package br.com.pedroabib.osmanager.os_manager.repositories;

import br.com.pedroabib.osmanager.os_manager.entities.Equipment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface EquipmentRepository extends JpaRepository<Equipment, Long> {
    
    Optional<Equipment> findBySerialNumber(String serialNumber);
}
