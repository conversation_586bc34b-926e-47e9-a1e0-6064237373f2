package br.com.pedroabib.osmanager.os_manager.repositories;

import br.com.pedroabib.osmanager.os_manager.entities.Technician;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface TechnicianRepository extends JpaRepository<Technician, Long> {
    
    Optional<Technician> findByEmail(String email);
}
