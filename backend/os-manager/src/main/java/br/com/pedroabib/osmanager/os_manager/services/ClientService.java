package br.com.pedroabib.osmanager.os_manager.services;

import br.com.pedroabib.osmanager.os_manager.dto.ClientCreateDTO;
import br.com.pedroabib.osmanager.os_manager.dto.ClientResponseDTO;
import br.com.pedroabib.osmanager.os_manager.dto.ClientUpdateDTO;
import br.com.pedroabib.osmanager.os_manager.entities.Client;
import br.com.pedroabib.osmanager.os_manager.repositories.ClientRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional
public class ClientService {

    private final ClientRepository clientRepository;

    public ClientResponseDTO createClient(ClientCreateDTO createDTO) {
        // Validar se email já existe
        if (createDTO.getEmail() != null && clientRepository.existsByEmail(createDTO.getEmail())) {
            throw new RuntimeException("Email já está em uso: " + createDTO.getEmail());
        }

        // Validar campos obrigatórios
        if (createDTO.getName() == null || createDTO.getName().trim().isEmpty()) {
            throw new RuntimeException("Nome é obrigatório");
        }

        Client client = new Client();
        client.setName(createDTO.getName().trim());
        client.setEmail(createDTO.getEmail());
        client.setPhone(createDTO.getPhone());
        client.setAddress(createDTO.getAddress());

        Client savedClient = clientRepository.save(client);
        return convertToResponseDTO(savedClient);
    }

    @Transactional(readOnly = true)
    public List<ClientResponseDTO> getAllClients() {
        List<Client> clients = clientRepository.findAll();
        return clients.stream()
                .map(this::convertToResponseDTO)
                .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public ClientResponseDTO getClientById(Long id) {
        Client client = clientRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Cliente não encontrado com ID: " + id));
        return convertToResponseDTO(client);
    }

    public ClientResponseDTO updateClient(Long id, ClientUpdateDTO updateDTO) {
        Client client = clientRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Cliente não encontrado com ID: " + id));

        // Validar email único se estiver sendo alterado
        if (updateDTO.getEmail() != null && !updateDTO.getEmail().equals(client.getEmail())) {
            if (clientRepository.existsByEmail(updateDTO.getEmail())) {
                throw new RuntimeException("Email já está em uso: " + updateDTO.getEmail());
            }
        }

        // Atualizar campos se fornecidos
        if (updateDTO.getName() != null && !updateDTO.getName().trim().isEmpty()) {
            client.setName(updateDTO.getName().trim());
        }
        if (updateDTO.getEmail() != null) {
            client.setEmail(updateDTO.getEmail());
        }
        if (updateDTO.getPhone() != null) {
            client.setPhone(updateDTO.getPhone());
        }
        if (updateDTO.getAddress() != null) {
            client.setAddress(updateDTO.getAddress());
        }

        Client updatedClient = clientRepository.save(client);
        return convertToResponseDTO(updatedClient);
    }

    public void deleteClient(Long id) {
        Client client = clientRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Cliente não encontrado com ID: " + id));

        // Verificar se tem ordens de serviço associadas
        if (client.getServiceOrders() != null && !client.getServiceOrders().isEmpty()) {
            throw new RuntimeException("Não é possível excluir cliente com ordens de serviço associadas");
        }

        clientRepository.delete(client);
    }

    @Transactional(readOnly = true)
    public List<ClientResponseDTO> searchClientsByName(String name) {
        List<Client> clients = clientRepository.findByNameContainingIgnoreCase(name);
        return clients.stream()
                .map(this::convertToResponseDTO)
                .collect(Collectors.toList());
    }

    private ClientResponseDTO convertToResponseDTO(Client client) {
        ClientResponseDTO dto = new ClientResponseDTO();
        dto.setId(client.getId());
        dto.setName(client.getName());
        dto.setEmail(client.getEmail());
        dto.setPhone(client.getPhone());
        dto.setAddress(client.getAddress());
        dto.setCreatedAt(client.getCreatedAt());
        
        // Contar ordens de serviço
        if (client.getServiceOrders() != null) {
            dto.setTotalServiceOrders(client.getServiceOrders().size());
        } else {
            dto.setTotalServiceOrders(0);
        }

        return dto;
    }
}
