package br.com.pedroabib.osmanager.os_manager.services;

import br.com.pedroabib.osmanager.os_manager.dto.ServiceOrderCreateDTO;
import br.com.pedroabib.osmanager.os_manager.dto.ServiceOrderResponseDTO;
import br.com.pedroabib.osmanager.os_manager.dto.ServiceOrderStatusUpdateDTO;
import br.com.pedroabib.osmanager.os_manager.entities.Client;
import br.com.pedroabib.osmanager.os_manager.entities.Equipment;
import br.com.pedroabib.osmanager.os_manager.entities.ServiceOrder;
import br.com.pedroabib.osmanager.os_manager.entities.Technician;
import br.com.pedroabib.osmanager.os_manager.repositories.ClientRepository;
import br.com.pedroabib.osmanager.os_manager.repositories.EquipmentRepository;
import br.com.pedroabib.osmanager.os_manager.repositories.ServiceOrderRepository;
import br.com.pedroabib.osmanager.os_manager.repositories.TechnicianRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional
public class ServiceOrderService {

    private final ServiceOrderRepository serviceOrderRepository;
    private final ClientRepository clientRepository;
    private final TechnicianRepository technicianRepository;
    private final EquipmentRepository equipmentRepository;

    public ServiceOrderResponseDTO createServiceOrder(ServiceOrderCreateDTO createDTO) {
        // Buscar client
        Client client = clientRepository.findById(createDTO.getClientId())
                .orElseThrow(() -> new RuntimeException("Cliente não encontrado com ID: " + createDTO.getClientId()));

        // Buscar technician (opcional)
        Technician technician = null;
        if (createDTO.getTechnicianId() != null) {
            technician = technicianRepository.findById(createDTO.getTechnicianId())
                    .orElseThrow(() -> new RuntimeException("Técnico não encontrado com ID: " + createDTO.getTechnicianId()));
        }

        // Buscar equipments (opcional)
        List<Equipment> equipments = null;
        if (createDTO.getEquipmentIds() != null && !createDTO.getEquipmentIds().isEmpty()) {
            equipments = equipmentRepository.findAllById(createDTO.getEquipmentIds());
            if (equipments.size() != createDTO.getEquipmentIds().size()) {
                throw new RuntimeException("Alguns equipamentos não foram encontrados");
            }
        }

        // Criar ServiceOrder
        ServiceOrder serviceOrder = new ServiceOrder();
        serviceOrder.setClient(client);
        serviceOrder.setTechnician(technician);
        serviceOrder.setEquipments(equipments);
        serviceOrder.setDescription(createDTO.getDescription());
        serviceOrder.setObservations(createDTO.getObservations());

        ServiceOrder savedServiceOrder = serviceOrderRepository.save(serviceOrder);
        return convertToResponseDTO(savedServiceOrder);
    }

    @Transactional(readOnly = true)
    public List<ServiceOrderResponseDTO> getAllServiceOrders() {
        List<ServiceOrder> serviceOrders = serviceOrderRepository.findAllWithDetails();
        return serviceOrders.stream()
                .map(this::convertToResponseDTO)
                .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public ServiceOrderResponseDTO getServiceOrderById(Long id) {
        ServiceOrder serviceOrder = serviceOrderRepository.findByIdWithDetails(id)
                .orElseThrow(() -> new RuntimeException("Ordem de serviço não encontrada com ID: " + id));
        return convertToResponseDTO(serviceOrder);
    }

    public ServiceOrderResponseDTO updateServiceOrderStatus(Long id, ServiceOrderStatusUpdateDTO statusUpdateDTO) {
        ServiceOrder serviceOrder = serviceOrderRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Ordem de serviço não encontrada com ID: " + id));

        serviceOrder.setStatus(statusUpdateDTO.getStatus());
        if (statusUpdateDTO.getObservations() != null) {
            serviceOrder.setObservations(statusUpdateDTO.getObservations());
        }

        ServiceOrder updatedServiceOrder = serviceOrderRepository.save(serviceOrder);
        return convertToResponseDTO(updatedServiceOrder);
    }

    private ServiceOrderResponseDTO convertToResponseDTO(ServiceOrder serviceOrder) {
        ServiceOrderResponseDTO dto = new ServiceOrderResponseDTO();
        dto.setId(serviceOrder.getId());
        dto.setOrderNumber(serviceOrder.getOrderNumber());
        dto.setCreatedAt(serviceOrder.getCreatedAt());
        dto.setStatus(serviceOrder.getStatus());
        dto.setDescription(serviceOrder.getDescription());
        dto.setObservations(serviceOrder.getObservations());

        // Client info
        if (serviceOrder.getClient() != null) {
            dto.setClientId(serviceOrder.getClient().getId());
            dto.setClientName(serviceOrder.getClient().getName());
            dto.setClientEmail(serviceOrder.getClient().getEmail());
        }

        // Technician info
        if (serviceOrder.getTechnician() != null) {
            dto.setTechnicianId(serviceOrder.getTechnician().getId());
            dto.setTechnicianName(serviceOrder.getTechnician().getName());
            dto.setTechnicianEmail(serviceOrder.getTechnician().getEmail());
        }

        // Equipment info
        if (serviceOrder.getEquipments() != null && !serviceOrder.getEquipments().isEmpty()) {
            List<ServiceOrderResponseDTO.EquipmentSummaryDTO> equipmentDTOs = serviceOrder.getEquipments().stream()
                    .map(equipment -> new ServiceOrderResponseDTO.EquipmentSummaryDTO(
                            equipment.getId(),
                            equipment.getName(),
                            equipment.getModel(),
                            equipment.getBrand(),
                            equipment.getSerialNumber()
                    ))
                    .collect(Collectors.toList());
            dto.setEquipments(equipmentDTOs);
        }

        return dto;
    }
}
